<template>
  <div id="rose-chart">
    <div class="rose-chart-title">学生请假情况</div>
    <dv-charts :option="option" />
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  data () {
    return {
      option: {}
    }
  },
  methods: {
    createData () {
      this.option = {
        grid: {
          left: '15%',
          right: '10%',
          top: '20%',
          bottom: '25%'
        },
        xAxis: {
          type: 'category',
          data: ['电子信息工程学院', '机械制造工程学院', '云南化工学院', '商务管理学院', '生物工程学院'],
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            interval: 0,
            rotate: 45
          },
          axisLine: {
            lineStyle: {
              color: '#4a90e2'
            }
          }
        },
        yAxis: {
          type: 'value',
          max: 16,
          axisLabel: {
            color: '#fff',
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: '#4a90e2'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: [15, 12, 8, 6, 4],
            itemStyle: {
              color: '#1e7ce8'
            },
            barWidth: '40%'
          }
        ]
      }
    }
  },
  mounted () {
    const { createData } = this

    createData()

    setInterval(createData, 30000)
  }
}
</script>

<style lang="less">
#rose-chart {
  width: 30%;
  height: 100%;
  background-color: rgba(6, 30, 93, 0.5);
  border-top: 2px solid rgba(1, 153, 209, .5);
  box-sizing: border-box;

  .rose-chart-title {
    height: 50px;
    font-weight: bold;
    text-indent: 20px;
    font-size: 20px;
    display: flex;
    align-items: center;
  }

  .dv-charts-container {
    height: calc(~"100% - 50px");
  }
}
</style>
